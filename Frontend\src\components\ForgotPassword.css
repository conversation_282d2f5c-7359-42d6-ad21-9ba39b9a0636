/* Forgot Password Component Styles */

.forgot-password-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9fafb;
  padding: 3rem 1rem;
}

.forgot-password-card {
  max-width: 28rem;
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 2rem;
}

.forgot-password-header {
  text-align: center;
  margin-bottom: 2rem;
}

.forgot-password-icon {
  margin: 0 auto 1.5rem;
  height: 3rem;
  width: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #dbeafe;
}

.forgot-password-icon svg {
  height: 1.5rem;
  width: 1.5rem;
  color: #2563eb;
}

.forgot-password-title {
  font-family: 'Baskerville Old Face', serif;
  font-size: 1.875rem;
  font-weight: 800;
  color: #111827;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.forgot-password-subtitle {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.forgot-password-form {
  margin-top: 2rem;
}

.forgot-password-field {
  margin-bottom: 1.5rem;
}

.forgot-password-label {
  font-family: 'Source Sans Pro', sans-serif;
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.forgot-password-input {
  font-family: 'Source Sans Pro', sans-serif;
  appearance: none;
  position: relative;
  display: block;
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #111827;
  font-size: 0.875rem;
  background-color: white;
  transition: all 0.15s ease-in-out;
  box-sizing: border-box;
}

.forgot-password-input::placeholder {
  color: #9ca3af;
}

.forgot-password-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.forgot-password-button {
  font-family: 'Merriweather', serif;
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.75rem 1rem;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 6px;
  color: white;
  background-color: #2563eb;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  min-height: 2.75rem;
}

.forgot-password-button:hover:not(:disabled) {
  background-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.forgot-password-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3);
}

.forgot-password-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.forgot-password-links {
  text-align: center;
  margin-top: 1.5rem;
}

.forgot-password-link {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  color: #2563eb;
  text-decoration: none;
  transition: color 0.15s ease-in-out;
}

.forgot-password-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

/* Success State Styles */
.forgot-password-success {
  text-align: center;
}

.forgot-password-success-icon {
  margin: 0 auto 1.5rem;
  height: 3rem;
  width: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #dcfce7;
}

.forgot-password-success-icon svg {
  height: 1.5rem;
  width: 1.5rem;
  color: #16a34a;
}

.forgot-password-success-title {
  font-family: 'Baskerville Old Face', serif;
  font-size: 1.875rem;
  font-weight: 800;
  color: #111827;
  margin: 0 0 0.5rem 0;
}

.forgot-password-success-text {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 2rem 0;
}

.forgot-password-info-box {
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  padding: 1rem;
  margin: 2rem 0;
}

.forgot-password-info-header {
  display: flex;
  align-items: flex-start;
}

.forgot-password-info-icon {
  flex-shrink: 0;
  margin-right: 0.75rem;
}

.forgot-password-info-icon svg {
  height: 1.25rem;
  width: 1.25rem;
  color: #60a5fa;
}

.forgot-password-info-content h3 {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e40af;
  margin: 0 0 0.5rem 0;
}

.forgot-password-info-list {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.875rem;
  color: #1e40af;
  margin: 0;
  padding-left: 1rem;
}

.forgot-password-info-list li {
  margin-bottom: 0.25rem;
}

.forgot-password-actions {
  text-align: center;
  margin-top: 2rem;
}

.forgot-password-actions p {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 0.5rem 0;
}

.forgot-password-retry-button {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  color: #2563eb;
  background: none;
  border: none;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.15s ease-in-out;
}

.forgot-password-retry-button:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 640px) {
  .forgot-password-container {
    padding: 1.5rem 1rem;
  }
  
  .forgot-password-card {
    padding: 1.5rem;
  }
  
  .forgot-password-title {
    font-size: 1.5rem;
  }
}

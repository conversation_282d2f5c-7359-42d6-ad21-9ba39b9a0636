# Production requirements for Lawvriksh Backend
# Optimized for Ubuntu 24.04 deployment

# Core FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
pymysql==1.1.0
cryptography==41.0.7

# Caching and Storage
diskcache==5.6.3
redis==5.0.1

# Background Tasks
celery==5.3.4
kombu==5.3.4

# Configuration and Environment
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Authentication and Security
PyJWT==2.8.0
passlib[bcrypt]==1.7.4
email-validator==2.1.0

# HTTP Client
httpx==0.25.2
requests==2.31.0

# Monitoring and Metrics
prometheus-client==0.19.0

# Email
aiosmtplib==3.0.1

# Utilities
python-multipart==0.0.6
python-jose[cryptography]==3.3.0

# Production WSGI/ASGI servers
uvloop==0.19.0  # For better async performance on Linux
httptools==0.6.1  # For better HTTP parsing

# Logging and Monitoring
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Development and Testing (optional for production)
pytest==7.4.3
pytest-asyncio==0.21.1

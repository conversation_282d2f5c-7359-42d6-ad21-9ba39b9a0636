# Lawvriksh Backend Production Environment Configuration
# =======================================================
# Copy this file to .env.production and update the values
# This file contains production-specific environment variables

# Domain Configuration
DOMAIN=www.lawvriksh.com
API_BASE_URL=https://www.lawvriksh.com/api
FRONTEND_URL=https://www.lawvriksh.com

# Database Configuration
DB_NAME=lawvriksh_production
DB_USER=lawvriksh_user
DB_PASSWORD=your_secure_database_password_here
MYSQL_ROOT_PASSWORD=your_secure_root_password_here

# Message Queue Configuration
RABBITMQ_USER=lawvriksh_mq
RABBITMQ_PASSWORD=your_secure_rabbitmq_password_here

# Cache Configuration
REDIS_PASSWORD=your_secure_redis_password_here

# Security Configuration
# Generate with: python -c "import secrets; print(secrets.token_urlsafe(64))"
JWT_SECRET_KEY=your_super_secure_jwt_secret_key_here_make_it_very_long_and_random

# Email Configuration
EMAIL_FROM=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_specific_password

# Application Settings
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Security Settings
ALLOWED_HOSTS=www.lawvriksh.com,lawvriksh.com

# Optional: Monitoring and Analytics
SENTRY_DSN=your_sentry_dsn_here_for_error_tracking

# Optional: External Services
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_S3_BUCKET=your_s3_bucket_name

# Optional: Additional Email Providers
# SENDGRID_API_KEY=your_sendgrid_api_key
# MAILGUN_API_KEY=your_mailgun_api_key
# MAILGUN_DOMAIN=your_mailgun_domain

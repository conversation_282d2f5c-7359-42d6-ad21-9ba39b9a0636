.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin: 1rem 0;
}

.error-boundary-content {
  text-align: center;
  max-width: 500px;
}

.error-boundary-content h2 {
  color: #dc3545;
  margin-bottom: 1rem;
  font-family: 'Baskerville Old Face', serif;
}

.error-boundary-content p {
  color: #6c757d;
  margin-bottom: 1.5rem;
  font-family: '<PERSON><PERSON>', sans-serif;
  font-style: italic;
}

.error-details {
  margin: 1rem 0;
  text-align: left;
}

.error-details summary {
  cursor: pointer;
  color: #007bff;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.error-details pre {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 1rem;
  font-size: 0.875rem;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

.retry-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-family: 'Merriweather', serif;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background-color: #0056b3;
}

.retry-button:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Font Imports */
@import url("https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Josefin+Sans:ital,wght@0,400;1,400&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Battambang:wght@400;700&display=swap");

/* Feedback Page Styles */
.feedback {
  min-height: 100vh;
  background-color: #fdfbf4;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="0.5" fill="%23966f33" opacity="0.1"/><circle cx="80" cy="40" r="0.3" fill="%23937643" opacity="0.08"/><circle cx="40" cy="80" r="0.4" fill="%23966f33" opacity="0.06"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 40px 20px;
  font-family: 'Source Sans Pro', sans-serif;
}

.feedback__container {
  max-width: 900px;
  width: 100%;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.08) 100%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 20px 60px rgba(150, 111, 51, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(24px) saturate(1.3);
  -webkit-backdrop-filter: blur(24px) saturate(1.3);
  border-radius: 0; /* Sharp rectangular corners */
  padding: 60px;
  margin: 20px 0;
}

/* Header Styles */
.feedback__header {
  text-align: center;
  margin-bottom: 50px;
}

.feedback__title {
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  color: #966f33;
  font-weight: 400;
  margin: 0 0 25px 0;
  letter-spacing: -0.02em;
  text-shadow: 0 2px 8px rgba(150, 111, 51, 0.3);
}

.feedback__subtitle {
  font-family: 'Josefin Sans', sans-serif;
  font-style: italic;
  font-size: clamp(1.1rem, 2.5vw, 1.3rem);
  color: #3e3e3e;
  line-height: 1.6;
  margin: 0;
  max-width: 800px;
  margin: 0 auto;
}

/* Form Styles */
.feedback__form {
  display: flex;
  flex-direction: column;
  gap: 50px;
}

.feedback__form-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.feedback__section-title {
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: clamp(1.8rem, 3vw, 2.2rem);
  color: #966f33;
  font-weight: 400;
  margin: 0 0 10px 0;
  letter-spacing: -0.01em;
}

.feedback__section-subtitle {
  font-family: 'Josefin Sans', sans-serif;
  font-style: italic;
  font-size: clamp(1rem, 2vw, 1.1rem);
  color: #5a5a5a;
  line-height: 1.5;
  margin: 0 0 20px 0;
}

/* Question Group Styles */
.feedback__question-group {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(150, 111, 51, 0.2);
  padding: 30px;
  border-radius: 0; /* Sharp rectangular corners */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.feedback__question-title {
  font-family: 'Battambang', sans-serif;
  font-size: clamp(1.1rem, 2vw, 1.3rem);
  color: #2c2c2c;
  font-weight: 400;
  line-height: 1.4;
  margin: 0 0 20px 0;
  letter-spacing: -0.01em;
}

/* Options Styles */
.feedback__options {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.feedback__option {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  padding: 12px;
  border-radius: 0; /* Sharp rectangular corners */
  transition: all 0.3s ease;
}

.feedback__option:hover {
  background: rgba(150, 111, 51, 0.08);
}

.feedback__radio {
  margin-top: 2px;
  width: 18px;
  height: 18px;
  accent-color: #966f33;
  cursor: pointer;
}

.feedback__option-text {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: clamp(0.95rem, 1.8vw, 1.05rem);
  color: #3e3e3e;
  line-height: 1.5;
  flex: 1;
}

/* Other Input Styles */
.feedback__other-input {
  margin-top: 15px;
  margin-left: 30px;
}

.feedback__text-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid rgba(150, 111, 51, 0.3);
  border-radius: 0; /* Sharp rectangular corners */
  background: rgba(255, 255, 255, 0.8);
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 1rem;
  color: #3e3e3e;
  transition: all 0.3s ease;
}

.feedback__text-input:focus {
  outline: none;
  border-color: #966f33;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 3px rgba(150, 111, 51, 0.1);
}

.feedback__text-input::placeholder {
  color: #999;
  font-style: italic;
}

/* Textarea Styles */
.feedback__textarea {
  width: 100%;
  padding: 16px;
  border: 2px solid rgba(150, 111, 51, 0.3);
  border-radius: 0; /* Sharp rectangular corners */
  background: rgba(255, 255, 255, 0.8);
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 1rem;
  color: #3e3e3e;
  line-height: 1.6;
  resize: vertical;
  min-height: 120px;
  transition: all 0.3s ease;
}

.feedback__textarea:focus {
  outline: none;
  border-color: #966f33;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 3px rgba(150, 111, 51, 0.1);
}

.feedback__textarea::placeholder {
  color: #999;
  font-style: italic;
}

/* Error Styles */
.feedback__error {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.9rem;
  color: #d32f2f;
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(211, 47, 47, 0.1);
  border-left: 3px solid #d32f2f;
  border-radius: 0; /* Sharp rectangular corners */
}

/* Submit Section */
.feedback__submit-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding-top: 30px;
  border-top: 1px solid rgba(150, 111, 51, 0.2);
}

.feedback__submit-error {
  text-align: center;
  max-width: 500px;
}

/* Success Message Styles */
.feedback__success-message {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.feedback__success-card {
  text-align: center;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 60px 40px;
  border-radius: 0; /* Sharp rectangular corners */
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(150, 111, 51, 0.15);
}

.feedback__success-title {
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: clamp(2rem, 4vw, 3rem);
  color: #966f33;
  font-weight: 400;
  margin: 0 0 20px 0;
  letter-spacing: -0.02em;
}

.feedback__success-description {
  font-family: 'Josefin Sans', sans-serif;
  font-style: italic;
  font-size: clamp(1.1rem, 2.5vw, 1.3rem);
  color: #3e3e3e;
  line-height: 1.6;
  margin: 0 0 40px 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 40px;
}

.feedback__success-actions {
  display: flex;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .feedback__container {
    padding: 50px 40px;
  }
}

@media (max-width: 768px) {
  .feedback {
    padding: 20px 15px;
  }

  .feedback__container {
    padding: 40px 25px;
    margin: 10px 0;
  }

  .feedback__header {
    margin-bottom: 40px;
  }

  .feedback__form {
    gap: 40px;
  }

  .feedback__form-section {
    gap: 25px;
  }

  .feedback__question-group {
    padding: 25px 20px;
  }

  .feedback__options {
    gap: 12px;
  }

  .feedback__option {
    padding: 10px 8px;
  }

  .feedback__option-text {
    font-size: 0.95rem;
  }

  .feedback__other-input {
    margin-left: 20px;
  }

  .feedback__textarea {
    padding: 14px;
    min-height: 100px;
  }

  .feedback__success-card {
    padding: 40px 25px;
  }
}

@media (max-width: 480px) {
  .feedback {
    padding: 15px 10px;
  }

  .feedback__container {
    padding: 30px 20px;
  }

  .feedback__title {
    font-size: 2rem;
  }

  .feedback__subtitle {
    font-size: 1rem;
  }

  .feedback__section-title {
    font-size: 1.5rem;
  }

  .feedback__section-subtitle {
    font-size: 0.95rem;
  }

  .feedback__question-group {
    padding: 20px 15px;
  }

  .feedback__question-title {
    font-size: 1.05rem;
  }

  .feedback__option {
    padding: 8px 5px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .feedback__radio {
    width: 16px;
    height: 16px;
  }

  .feedback__option-text {
    font-size: 0.9rem;
    margin-left: 0;
  }

  .feedback__other-input {
    margin-left: 0;
    margin-top: 10px;
  }

  .feedback__text-input,
  .feedback__textarea {
    padding: 12px;
    font-size: 0.95rem;
  }

  .feedback__textarea {
    min-height: 90px;
  }

  .feedback__success-card {
    padding: 30px 20px;
  }

  .feedback__success-title {
    font-size: 1.8rem;
  }

  .feedback__success-description {
    font-size: 1rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .feedback__container {
    border: 2px solid #966f33;
    background: rgba(255, 255, 255, 0.95);
  }

  .feedback__question-group {
    border: 1px solid #966f33;
    background: rgba(255, 255, 255, 0.8);
  }

  .feedback__text-input,
  .feedback__textarea {
    border: 2px solid #966f33;
    background: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .feedback__container,
  .feedback__form-section,
  .feedback__option,
  .feedback__text-input,
  .feedback__textarea {
    transition: none;
  }
}

/* Print styles */
@media print {
  .feedback {
    background: white;
    padding: 0;
  }

  .feedback__container {
    background: white;
    border: 1px solid #000;
    box-shadow: none;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    padding: 20px;
  }

  .feedback__question-group {
    background: white;
    border: 1px solid #ccc;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    page-break-inside: avoid;
  }

  .feedback__submit-section {
    display: none;
  }
}

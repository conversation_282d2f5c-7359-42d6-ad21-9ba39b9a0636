/* Campaign Management Component Styles */

.campaign-management {
  background: white;
  border-radius: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.campaign-tabs {
  border-bottom: 1px solid #e5e7eb;
}

.campaign-tabs nav {
  display: flex;
  gap: 2rem;
  padding: 0 1.5rem;
}

.campaign-tab {
  padding: 1rem 0.25rem;
  border-bottom: 2px solid transparent;
  font-weight: 500;
  font-size: 0.875rem;
  color: #6b7280;
  background: none;
  border-left: none;
  border-right: none;
  border-top: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Source Sans Pro', sans-serif;
}

.campaign-tab:hover {
  color: #374151;
  border-bottom-color: #d1d5db;
}

.campaign-tab.active {
  color: #d4af37;
  border-bottom-color: #d4af37;
}

.campaign-content {
  padding: 1.5rem;
}

/* Loading State */
.campaign-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 16rem;
}

/* Schedule Section */
.campaign-schedule-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.campaign-schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.campaign-schedule-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  font-family: 'Battambang', sans-serif;
}

.campaign-refresh-button {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #2563eb;
  background: #eff6ff;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Source Sans Pro', sans-serif;
}

.campaign-refresh-button:hover {
  background: #dbeafe;
}

.campaign-schedule-grid {
  display: grid;
  gap: 1rem;
}

.campaign-schedule-item {
  border: 1px solid #e5e7eb;
  border-radius: 0;
  padding: 1rem;
}

.campaign-schedule-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.campaign-schedule-item-content {
  flex: 1;
}

.campaign-schedule-item-title-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.campaign-schedule-item-title {
  font-weight: 500;
  color: #111827;
  text-transform: capitalize;
  font-family: 'Battambang', sans-serif;
}

.campaign-status-badge {
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.campaign-status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.campaign-status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.campaign-status-badge.completed {
  background: #dbeafe;
  color: #1e40af;
}

.campaign-due-badge {
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
  background: #fed7aa;
  color: #9a3412;
}

.campaign-schedule-item-subject {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.campaign-schedule-item-schedule {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.campaign-test-button {
  margin-left: 1rem;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #2563eb;
  background: #eff6ff;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Source Sans Pro', sans-serif;
}

.campaign-test-button:hover:not(:disabled) {
  background: #dbeafe;
}

.campaign-test-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.campaign-due-campaigns {
  background: #fff7ed;
  border: 1px solid #fed7aa;
  border-radius: 0;
  padding: 1rem;
}

.campaign-due-campaigns-title {
  font-weight: 500;
  color: #9a3412;
  font-family: 'Battambang', sans-serif;
}

.campaign-due-campaigns-text {
  font-size: 0.875rem;
  color: #c2410c;
  margin-top: 0.25rem;
}

/* Send Section */
.campaign-send-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.campaign-send-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  font-family: 'Battambang', sans-serif;
}

.campaign-send-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .campaign-send-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.campaign-send-form-group {
  display: flex;
  flex-direction: column;
}

.campaign-send-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-family: 'Source Sans Pro', sans-serif;
}

.campaign-send-select,
.campaign-send-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0;
  font-size: 0.875rem;
  font-family: 'Source Sans Pro', sans-serif;
  background: white;
  color: #374151;
}

.campaign-send-select:focus,
.campaign-send-input:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.campaign-send-button {
  width: 100%;
  padding: 0.5rem 1.5rem;
  background: #2563eb;
  color: white;
  font-weight: 500;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Source Sans Pro', sans-serif;
}

@media (min-width: 768px) {
  .campaign-send-button {
    width: auto;
  }
}

.campaign-send-button:hover:not(:disabled) {
  background: #1d4ed8;
}

.campaign-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Preview Section */
.campaign-preview-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.campaign-preview-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  font-family: 'Battambang', sans-serif;
}

.campaign-preview-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.campaign-preview-card {
  border-radius: 0;
  padding: 1rem;
}

.campaign-preview-card.instant {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.campaign-preview-card.future {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
}

.campaign-preview-card.past {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
}

.campaign-preview-card-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
  font-family: 'Battambang', sans-serif;
}

.campaign-preview-card.instant .campaign-preview-card-title {
  color: #14532d;
}

.campaign-preview-card.future .campaign-preview-card-title {
  color: #1e3a8a;
}

.campaign-preview-card.past .campaign-preview-card-title {
  color: #111827;
}

.campaign-preview-card-note {
  font-size: 0.75rem;
  margin-bottom: 0.75rem;
}

.campaign-preview-card.instant .campaign-preview-card-note {
  color: #16a34a;
}

.campaign-preview-card.future .campaign-preview-card-note {
  color: #2563eb;
}

.campaign-preview-card.past .campaign-preview-card-note {
  color: #6b7280;
}

.campaign-preview-campaigns-grid {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.campaign-preview-campaign-item {
  background: white;
  border-radius: 0;
  padding: 0.75rem;
}

.campaign-preview-campaign-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.campaign-preview-campaign-content {
  flex: 1;
}

.campaign-preview-campaign-subject {
  font-weight: 500;
  color: #111827;
}

.campaign-preview-campaign-schedule {
  font-size: 0.875rem;
  color: #6b7280;
}

.campaign-preview-campaign-badge {
  font-size: 0.75rem;
  background: #dbeafe;
  color: #1e40af;
  padding: 0.125rem 0.5rem;
  border-radius: 0;
}

.campaign-preview-past-text {
  font-size: 0.875rem;
  color: #374151;
}

/* Responsive Design */
@media (max-width: 768px) {
  .campaign-content {
    padding: 1rem;
  }

  .campaign-tabs nav {
    padding: 0 1rem;
    flex-wrap: wrap;
  }

  .campaign-schedule-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .campaign-send-grid {
    grid-template-columns: 1fr;
  }

  .campaign-schedule-item-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .campaign-test-button {
    margin-left: 0;
    align-self: flex-start;
  }
}

.campaign-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  background: white;
  transition: all 0.2s ease;
}

.campaign-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.campaign-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.campaign-info {
  flex: 1;
}

.campaign-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.25rem;
}

.campaign-name {
  font-weight: 500;
  color: #111827;
  text-transform: capitalize;
}

.campaign-status {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.status-active {
  background-color: #dcfce7;
  color: #166534;
}

.status-scheduled {
  background-color: #f3f4f6;
  color: #374151;
}

.status-due {
  background-color: #fed7aa;
  color: #9a3412;
}

.status-completed {
  background-color: #dcfce7;
  color: #166534;
}

.status-failed {
  background-color: #fecaca;
  color: #991b1b;
}

.campaign-subject {
  font-size: 0.875rem;
  color: #4b5563;
  margin-top: 0.25rem;
}

.campaign-schedule {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.test-button {
  margin-left: 1rem;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #2563eb;
  background-color: #eff6ff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-button:hover {
  background-color: #dbeafe;
}

.test-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-input,
.form-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.send-button {
  padding: 0.5rem 1.5rem;
  background-color: #2563eb;
  color: white;
  font-weight: 500;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-button:hover {
  background-color: #1d4ed8;
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-button.full-width {
  width: 100%;
}

@media (min-width: 768px) {
  .send-button.full-width {
    width: auto;
  }
}

.alert {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.alert-success {
  background-color: #dcfce7;
  border: 1px solid #bbf7d0;
  color: #166534;
}

.alert-warning {
  background-color: #fef3c7;
  border: 1px solid #fed7aa;
  color: #92400e;
}

.alert-error {
  background-color: #fecaca;
  border: 1px solid #fca5a5;
  color: #991b1b;
}

.alert-info {
  background-color: #dbeafe;
  border: 1px solid #93c5fd;
  color: #1e40af;
}

.preview-section {
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 8px;
}

.preview-instant {
  background-color: #dcfce7;
  border: 1px solid #bbf7d0;
}

.preview-future {
  background-color: #dbeafe;
  border: 1px solid #93c5fd;
}

.preview-past {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
}

.preview-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.preview-instant .preview-title {
  color: #166534;
}

.preview-future .preview-title {
  color: #1e40af;
}

.preview-past .preview-title {
  color: #374151;
}

.preview-note {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.preview-instant .preview-note {
  color: #15803d;
}

.preview-future .preview-note {
  color: #2563eb;
}

.preview-past .preview-note {
  color: #6b7280;
}

.campaign-item {
  background: white;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
}

.campaign-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.campaign-item-title {
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
}

.campaign-item-schedule {
  font-size: 0.875rem;
  color: #4b5563;
}

.days-badge {
  font-size: 0.75rem;
  background-color: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.refresh-button {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #2563eb;
  background-color: #eff6ff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background-color: #dbeafe;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 16rem;
}

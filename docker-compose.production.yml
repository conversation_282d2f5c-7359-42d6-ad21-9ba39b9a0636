version: '3.8'

# Production Docker Compose for Lawvriksh Backend
# Domain: www.lawvriksh.com/api/
# Optimized for Ubuntu 24.04 deployment

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: lawvriksh-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./logs/mysql:/var/log/mysql
    ports:
      - "127.0.0.1:3306:3306"  # Only bind to localhost for security
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=256M
      --max-connections=200
      --slow-query-log=1
      --slow-query-log-file=/var/log/mysql/slow.log
      --long-query-time=2
    networks:
      - lawvriksh-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      timeout: 20s
      retries: 10

  # RabbitMQ Message Queue
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: lawvriksh-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
      RABBITMQ_DEFAULT_VHOST: lawvriksh
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./logs/rabbitmq:/var/log/rabbitmq
    ports:
      - "127.0.0.1:5672:5672"    # AMQP port
      - "127.0.0.1:15672:15672"  # Management UI
    networks:
      - lawvriksh-network
    healthcheck:
      test: rabbitmq-diagnostics -q ping
      interval: 30s
      timeout: 30s
      retries: 3

  # Redis Cache (for session storage and caching)
  redis:
    image: redis:7-alpine
    container_name: lawvriksh-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./logs/redis:/var/log/redis
    ports:
      - "127.0.0.1:6379:6379"
    networks:
      - lawvriksh-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend Application
  backend:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        - BUILD_ENV=production
    container_name: lawvriksh-backend
    restart: unless-stopped
    environment:
      # Database Configuration
      DATABASE_URL: mysql+pymysql://${DB_USER}:${DB_PASSWORD}@mysql:3306/${DB_NAME}
      
      # Message Queue
      RABBITMQ_URL: amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/lawvriksh
      
      # Cache
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      
      # Security
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      
      # Email Configuration
      EMAIL_FROM: ${EMAIL_FROM}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASSWORD: ${SMTP_PASSWORD}
      
      # Application Settings
      ENVIRONMENT: production
      DEBUG: "false"
      CACHE_DIR: /app/cache
      LOG_LEVEL: INFO
      
      # Domain Configuration
      DOMAIN: ${DOMAIN}
      API_BASE_URL: https://${DOMAIN}/api
      FRONTEND_URL: https://${DOMAIN}
      
      # Security Headers
      ALLOWED_HOSTS: ${DOMAIN},www.${DOMAIN}
      
    volumes:
      - ./cache:/app/cache
      - ./logs/backend:/app/logs
      - ./uploads:/app/uploads
    ports:
      - "127.0.0.1:8000:8000"  # Only bind to localhost, Nginx will proxy
    depends_on:
      mysql:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - lawvriksh-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Celery Worker for Background Tasks
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        - BUILD_ENV=production
    container_name: lawvriksh-celery-worker
    restart: unless-stopped
    command: celery -A app.tasks.celery_app worker --loglevel=info --concurrency=2
    environment:
      # Same environment as backend
      DATABASE_URL: mysql+pymysql://${DB_USER}:${DB_PASSWORD}@mysql:3306/${DB_NAME}
      RABBITMQ_URL: amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/lawvriksh
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      EMAIL_FROM: ${EMAIL_FROM}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASSWORD: ${SMTP_PASSWORD}
      ENVIRONMENT: production
      DEBUG: "false"
      LOG_LEVEL: INFO
    volumes:
      - ./cache:/app/cache
      - ./logs/celery:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - lawvriksh-network

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        - BUILD_ENV=production
    container_name: lawvriksh-celery-beat
    restart: unless-stopped
    command: celery -A app.tasks.celery_app beat --loglevel=info
    environment:
      # Same environment as backend
      DATABASE_URL: mysql+pymysql://${DB_USER}:${DB_PASSWORD}@mysql:3306/${DB_NAME}
      RABBITMQ_URL: amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/lawvriksh
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      ENVIRONMENT: production
      DEBUG: "false"
      LOG_LEVEL: INFO
    volumes:
      - ./cache:/app/cache
      - ./logs/celery-beat:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - lawvriksh-network

# Networks
networks:
  lawvriksh-network:
    driver: bridge
    name: lawvriksh-network

# Volumes
volumes:
  mysql_data:
    driver: local
    name: lawvriksh-mysql-data
  rabbitmq_data:
    driver: local
    name: lawvriksh-rabbitmq-data
  redis_data:
    driver: local
    name: lawvriksh-redis-data

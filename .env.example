# Lawvriksh Backend Configuration
# Copy this file to .env and update the values

# Database Configuration
# MySQL Database (Primary) - Update with your credentials
DATABASE_URL=mysql+pymysql://your_user:your_password@localhost:3306/your_database

# Individual database parameters (for reference)
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_database_name
DB_HOST=localhost
DB_PORT=3306

# Security Configuration
# IMPORTANT: Generate a secure random key for production!
# You can generate one using: python -c "import secrets; print(secrets.token_urlsafe(32))"
JWT_SECRET_KEY=your-super-secret-key-here-make-it-long-and-random

# Message Queue Configuration
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# Email Configuration
EMAIL_FROM=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Application Settings
CACHE_DIR=./cache

# Environment (set to 'production' in production)
ENVIRONMENT=development

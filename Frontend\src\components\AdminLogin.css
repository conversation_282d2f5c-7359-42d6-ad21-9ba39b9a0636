.admin-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  padding: 20px;
}

.admin-login-card {
  background: white;
  border-radius: 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  padding: 40px;
  width: 100%;
  max-width: 450px;
  border: 2px solid #d4af37;
}

.admin-login-header {
  text-align: center;
  margin-bottom: 30px;
}

.admin-login-header h1 {
  font-family: 'Baskerville Old Face', serif;
  font-size: 2.5rem;
  color: #1a1a1a;
  margin: 0 0 10px 0;
  font-weight: normal;
}

.admin-login-header p {
  font-family: 'Josefin Sans', sans-serif;
  font-style: italic;
  color: #666;
  margin: 0;
  font-size: 1.1rem;
}

.admin-login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.95rem;
}

.form-group input {
  padding: 12px 16px;
  border: 2px solid #ddd;
  border-radius: 0;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #d4af37;
}

.form-group input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.error-message {
  background-color: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 12px;
  border-radius: 0;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.9rem;
  text-align: center;
}

.admin-login-btn {
  background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
  color: #1a1a1a;
  border: none;
  padding: 14px 24px;
  font-family: 'Merriweather', serif;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.admin-login-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #f4d03f 0%, #d4af37 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.admin-login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.admin-login-links {
  margin-top: 20px;
  text-align: center;
}

.forgot-password-link {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.9rem;
  color: #2563eb;
  text-decoration: none;
  transition: all 0.3s ease;
}

.forgot-password-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.admin-login-footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.admin-login-footer p {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.85rem;
  color: #666;
  margin: 4px 0;
}

.admin-login-footer p:first-child {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-login-container {
    padding: 10px;
  }
  
  .admin-login-card {
    padding: 30px 20px;
  }
  
  .admin-login-header h1 {
    font-size: 2rem;
  }
}
